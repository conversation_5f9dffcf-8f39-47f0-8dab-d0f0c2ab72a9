@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 0.3rem;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Modern scroll behavior */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Glass morphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Modern gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Responsive layout utilities */
  .responsive-container {
    @apply container mx-auto px-6 xl:px-20;
  }

  .balanced-layout {
    @apply flex items-center flex-col-reverse lg:flex-row gap-8 md:gap-12 lg:gap-16 xl:gap-20;
  }

  .content-section {
    @apply w-full md:w-4/5 lg:w-3/5 xl:w-1/2 2xl:w-3/5 lg:max-w-2xl xl:max-w-3xl mx-auto lg:mx-0;
  }

  .image-section {
    @apply relative flex-shrink-0;
  }

  /* Better spacing for different screen sizes */
  .main-content-container {
    @apply transition-all duration-300 ease-in-out;
  }
}

/* Enhanced animations */
.animate_in {
  animation: animateIn 0.6s cubic-bezier(0.16, 1, 0.3, 1) 0.15s both;
}

.animate_fade_up {
  animation: fadeUp 0.8s cubic-bezier(0.16, 1, 0.3, 1) both;
}

.animate_fade_in {
  animation: fadeIn 0.6s cubic-bezier(0.16, 1, 0.3, 1) both;
}

.animate_scale_in {
  animation: scaleIn 0.5s cubic-bezier(0.16, 1, 0.3, 1) both;
}

@keyframes animateIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Modern button hover effects */
.btn-modern {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.btn-modern::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-modern:hover::before {
  left: 100%;
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease,
    border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease,
    opacity 0.2s ease;
}

/* Performance optimizations for 60fps animations */
.project-card {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

.project-card-image {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Line clamp utility for consistent text truncation */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced hover effects */
.project-card:hover {
  transform: translateY(-1px) translateZ(0);
}

/* Optimized backdrop blur */
.backdrop-blur-optimized {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Responsive layout improvements */
@media (min-width: 1024px) {
  .main-content-container {
    padding-left: 4rem; /* Account for social sidebar */
  }

  /* Ensure social sidebar doesn't interfere with content */
  .social-sidebar {
    transform: translateX(0);
  }
}

@media (min-width: 1280px) {
  .main-content-container {
    padding-left: 5rem; /* Larger padding for XL screens */
  }
}

@media (min-width: 1536px) {
  .main-content-container {
    padding-left: 6rem; /* Even larger padding for 2XL screens */
  }
}

/* Ensure content doesn't get too wide on very large screens */
@media (min-width: 1920px) {
  .content-section {
    max-width: 60rem;
  }
}

/* Handle smaller large screens better */
@media (min-width: 1024px) and (max-width: 1279px) {
  .main-content-container {
    padding-left: 3rem;
    padding-right: 1rem;
  }

  .content-section {
    max-width: 28rem;
  }
}
